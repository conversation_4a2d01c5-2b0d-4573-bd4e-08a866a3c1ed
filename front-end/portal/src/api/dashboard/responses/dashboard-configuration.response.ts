import { PageType } from '../enums/page-type.enum';
import { SectionType } from '../enums/section-type.enum';
import { WidgetType } from '../enums/widget-type.enum';
import { DashboardBusinessType } from '@api/dashboard/enums/dashboard-business-type.enum';

export interface DashboardConfigurationResponse {
  [key: string]: {
    [key in SectionType]: {
      [key in WidgetType]: {
        has_kpis?: boolean;
        business_types?: DashboardBusinessType[];
      };
    };
  };
}
