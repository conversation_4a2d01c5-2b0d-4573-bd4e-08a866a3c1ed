import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from '@services/config.service';
import { AccountRequest } from '@api/profile/requests/account.request';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { Account } from '@api/profile/models/account.interface';

@Injectable({
  providedIn: 'root',
})
export class AccountService {
  private readonly endpoint?: string;

  private httpClient = inject(HttpClient);
  private configService = inject(ConfigService);

  constructor() {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public update(body: AccountRequest): Observable<DataResponse<Account>> {
    return this.httpClient.put<DataResponse<Account>>(
      `${this.endpoint}/api/v1/account`,
      body,
    );
  }
}
