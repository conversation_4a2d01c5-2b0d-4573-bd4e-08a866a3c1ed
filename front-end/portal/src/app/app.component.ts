import { Component, HostListener, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { TranslocoPipe } from '@jsverse/transloco';
import { AuthState } from '@states/auth.state';
import { CookieService } from 'ngx-cookie-service';
import { getDomain } from '@helpers/get-domain';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, TranslocoPipe],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent {
  private authService = inject(AuthState);
  private cookieService = inject(CookieService);

  @HostListener('window:beforeunload', ['$event'])
  public unloadHandler(): void {
    if (!this.authService.isImpersonating()) {
      return;
    }

    this.authService.logout();
    this.cookieService.delete('impersonating', '/', getDomain());
  }
}
