import { Component, DestroyRef, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import {
  TranslocoPipe,
  TranslocoDirective,
  TranslocoService,
} from '@jsverse/transloco';
import { AuthenticationService } from '@api/auth/services/authentication.service';

import { RegisterRequest } from '@api/auth/requests/register.request';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ToastService } from '@services/toast.service';

interface RegisterForm {
  name: FormControl<string | null>;
  firstname: FormControl<string | null>;
  lastname: FormControl<string | null>;
  email: FormControl<string | null>;
  password: FormControl<string | null>;
}

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterLink,
    TranslocoPipe,
    TranslocoDirective,
  ],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss'],
})
export class RegisterComponent implements OnInit {
  public form!: FormGroup<RegisterForm>;
  public loading: boolean = false;

  constructor(
    private router: Router,
    private authenticationService: AuthenticationService,
    private toastService: ToastService,
    private destroyRef: DestroyRef,
  ) {}

  public ngOnInit(): void {
    this.initForm();
  }

  private initForm(): void {
    this.form = new FormGroup<RegisterForm>({
      name: new FormControl<string | null>(null, [Validators.required]),
      firstname: new FormControl<string | null>(null, [Validators.required]),
      lastname: new FormControl<string | null>(null, [Validators.required]),
      email: new FormControl<string | null>(null, [
        Validators.required,
        Validators.email,
      ]),
      password: new FormControl<string | null>(null, [
        Validators.required,
        Validators.minLength(8),
      ]),
    });
  }

  public onSubmit(): void {
    if (!this.form || this.form.invalid) {
      this.form?.markAllAsTouched();
      return;
    }

    this.loading = true;

    this.authenticationService
      .register(this.form.value as RegisterRequest)
      .pipe(
        tap((response) => {
          this.loading = false;
          this.router.navigate(['auth', 'login']);
        }),
        catchError((err) => {
          let title = null;
          let description = null;

          if (err.status === 401 || err.status === 403) {
            title = 'pages.auth.login.toasts.error.title';
            description = 'pages.auth.login.toasts.error.' + err.status;
          }

          this.toastService.error(title, description);

          this.loading = false;
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
