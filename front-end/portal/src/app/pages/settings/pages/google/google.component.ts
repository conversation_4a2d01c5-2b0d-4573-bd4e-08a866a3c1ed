import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  OnInit,
  inject,
  signal,
} from '@angular/core';
import { GoogleAccountService } from '@api/google/services/google-account.service';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { GoogleAccount } from '@api/google/models/google-account.interface';
import { PaginationComponent } from '@app/components/pagination/pagination.component';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';

import { catchError, filter, Observable, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { GoogleAuthenticationService } from '@api/google/services/google-authentication.service';
import { DropdownComponent } from '../../../../components/dropdown/dropdown.component';
import { DeleteService } from './delete/delete.service';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-google',
  imports: [
    PaginationComponent,
    TranslocoDirective,
    TranslocoPipe,
    DropdownComponent,
  ],
  templateUrl: './google.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GoogleComponent implements OnInit {
  public response = signal<PaginatedResponse<GoogleAccount> | null>(null);
  public loading = signal<boolean>(false);

  private googleAccountService = inject(GoogleAccountService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private googleAuthenticationService = inject(GoogleAuthenticationService);
  private deleteService = inject(DeleteService);

  public ngOnInit(): void {
    this.loadAccounts();
  }

  public loadAccounts(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.googleAccountService
      .index({ page })
      .pipe(
        filter(
          (response): response is PaginatedResponse<GoogleAccount> => true,
        ),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public addAccount(): void {
    this.googleAuthenticationService
      .oauthRedirectLink()
      .pipe(
        tap((response) => {
          window.open(response.data.url, '_self');
        }),
        catchError((err) => {
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public async delete(account: GoogleAccount): Promise<void> {
    const result = await this.deleteService.show(account);

    if (!result) {
      return;
    }

    this.loadAccounts(this.response()?.meta.current_page ?? 1);
  }
}
