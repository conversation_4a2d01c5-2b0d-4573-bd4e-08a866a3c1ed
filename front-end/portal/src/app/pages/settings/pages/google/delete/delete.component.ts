import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { ModalComponent } from '../../../../../components/modal/modal.component';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { GoogleAccount } from '@api/google/models/google-account.interface';
import { catchError, Subject, tap } from 'rxjs';
import { GoogleAccountService } from '@api/google/services/google-account.service';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-delete',
  imports: [ModalComponent, TranslocoDirective, TranslocoPipe],
  templateUrl: './delete.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeleteComponent {
  public account!: GoogleAccount;
  public loading = signal<boolean>(false);

  public close$: Subject<boolean> = new Subject();

  private googleAccountService = inject(GoogleAccountService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);

  public close(value: boolean = false): void {
    this.close$.next(value);
    this.close$.complete();
  }

  public submit(): void {
    if (!this.account || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.googleAccountService
      .delete(this.account)
      .pipe(
        tap(() => {
          this.loading.set(false);
          this.close(true);
          this.toastService.success();
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
