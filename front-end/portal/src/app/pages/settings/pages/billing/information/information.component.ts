import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { StripeInformation } from '@api/billing/models/stripe-information.interface';
import { BillingService } from '@api/billing/services/billing.service';

import { AuthState } from '@states/auth.state';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { MethodService } from '@pages/settings/pages/billing/information/method/method.service';
import { ToastService } from '@services/toast.service';

interface Form {
  email: FormControl<string | null>;
  street: FormControl<string | null>;
  house_number: FormControl<number | null>;
  house_number_addition: FormControl<string | null>;
  city: FormControl<string | null>;
  zipcode: FormControl<string | null>;
  country: FormControl<string | null>;
}

@Component({
  selector: 'app-information',
  imports: [ReactiveFormsModule, TranslocoDirective, TranslocoPipe],
  templateUrl: './information.component.html',
})
export class InformationComponent implements OnInit {
  public information = signal<StripeInformation | null>(null);
  public loading = signal<boolean>(false);
  public form = signal<FormGroup<Form> | null>(null);

  private billingService = inject(BillingService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private authService = inject(AuthState);
  private methodService = inject(MethodService);

  public ngOnInit(): void {
    this.initForm();
  }

  public submit(): void {
    const form = this.form();

    if (!form || form.invalid || this.loading()) {
      form?.markAllAsTouched();
      return;
    }

    this.loading.set(true);

    this.billingService
      .update(form.value)
      .pipe(
        tap((response) => {
          this.toastService.success();
          this.loading.set(false);
          this.information.set(response.data);

          const user = this.authService.user();

          if (user && user.account) {
            user.account.stripe_information = response.data;
            this.authService.user.set(user);
          }
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public openMethod(): void {
    this.methodService.show();
  }

  private initForm(): void {
    this.information.set(
      this.authService.user()?.account?.stripe_information ?? null,
    );

    const form = new FormGroup<Form>({
      email: new FormControl(this.information()?.email ?? null, []),
      street: new FormControl(this.information()?.street ?? null, []),
      house_number: new FormControl(
        this.information()?.house_number ?? null,
        [],
      ),
      house_number_addition: new FormControl(
        this.information()?.house_number_addition ?? null,
      ),
      city: new FormControl(this.information()?.city ?? null, []),
      zipcode: new FormControl(this.information()?.zipcode ?? null, []),
      country: new FormControl(this.information()?.country ?? null, []),
    });

    this.form.set(form);
  }
}
