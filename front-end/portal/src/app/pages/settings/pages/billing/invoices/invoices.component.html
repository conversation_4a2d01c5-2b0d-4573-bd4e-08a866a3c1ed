<div *transloco="let t; prefix: 'pages.settings.billing.invoices'">
  @if (loading()) {
    <div class="mt-8 flex justify-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
    </div>
  } @else if (response()?.data && response()!.data.length > 0) {
    <div class="mt-8 flow-root">
      <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
          <div class="overflow-hidden shadow rounded-lg">
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ t('table.headers.invoice') }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ t('table.headers.status') }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ t('table.headers.amount') }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ t('table.headers.due_date') }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ t('table.headers.invoiced_date') }}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ t('table.headers.paid_date') }}
                </th>
                <th scope="col" class="relative px-6 py-3">
                  <span class="sr-only">{{ t('table.headers.actions') }}</span>
                </th>
              </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                @for (invoice of response()!.data; track invoice.id;) {
                  @if({loading: false}; as state) {
                    <tr class="hover:bg-gray-50">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex flex-col">
                          <div class="text-sm font-medium text-gray-900">
                            {{ invoice.number || t('table.values.draft') }}
                          </div>
                          <div class="text-sm text-gray-500">
                            {{ t('table.values.id_prefix') }}: {{ invoice.id }}
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium text-white"
                              [ngClass]="{
                                'bg-green-500': invoice.status === status.OPEN,
                                'bg-gray-500': invoice.status === status.VOID,
                                'bg-red-500': invoice.status === status.UNCOLLECTIBLE,
                                'bg-purple-600': invoice.status === status.DRAFT,
                                'bg-blue-500': invoice.status === status.PAID
                              }">
                          {{ t('status.' + invoice.status) }}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex flex-col">
                          <div class="text-sm font-medium text-gray-900">
                            €{{ (invoice.total_including_vat / 100) | number:'1.2-2' }}
                          </div>
                          <div class="text-sm text-gray-500">
                            {{ t('table.values.excl_vat') }}: €{{ (invoice.total_excluding_vat / 100) | number:'1.2-2' }}
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ (invoice.due_date | date: 'dd/MM/yyyy') || '-' }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ (invoice.invoiced_date | date:'dd/MM/yyyy') || '-' }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ (invoice.paid_at | date:'dd/MM/yyyy') || '-' }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        @if (invoice.status !== status.DRAFT && invoice.number) {
                          <button
                            type="button"
                            (click)="download(invoice, state)"
                            [disabled]="state.loading"
                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                            @if (state.loading) {
                              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-indigo-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              {{ t('table.actions.downloading') }}
                            } @else {
                              <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                              </svg>
                              {{ t('table.actions.download') }}
                            }
                          </button>
                        } @else {
                          <span class="text-gray-400 text-sm">{{ t('table.values.not_available') }}</span>
                        }
                      </td>
                    </tr>
                  }
                }
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    @if (response()!.meta && response()!.meta.last_page > 1) {
      <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
        <div class="flex flex-1 justify-between sm:hidden">
          <button
            (click)="loadInvoices(response()!.meta.current_page - 1)"
            [disabled]="response()!.meta.current_page <= 1 || loading()"
            class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            {{ t('pagination.previous') }}
          </button>
          <button
            (click)="loadInvoices(response()!.meta.current_page + 1)"
            [disabled]="response()!.meta.current_page >= response()!.meta.last_page || loading()"
            class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            {{ t('pagination.next') }}
          </button>
        </div>
        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              {{ t('pagination.showing') }}
              <span class="font-medium">{{ response()!.meta.from }}</span>
              {{ t('pagination.to') }}
              <span class="font-medium">{{ response()!.meta.to }}</span>
              {{ t('pagination.of') }}
              <span class="font-medium">{{ response()!.meta.total }}</span>
              {{ t('pagination.results') }}
            </p>
          </div>
          <div>
            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
              <button
                (click)="loadInvoices(response()!.meta.current_page - 1)"
                [disabled]="response()!.meta.current_page <= 1 || loading()"
                class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <span class="sr-only">{{ t('pagination.previous') }}</span>
                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                </svg>
              </button>

              @for (page of [].constructor(response()!.meta.last_page); track $index; let i = $index) {
                @if (i + 1 === response()!.meta.current_page) {
                  <button
                    aria-current="page"
                    class="relative z-10 inline-flex items-center bg-indigo-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                    {{ i + 1 }}
                  </button>
                } @else {
                  <button
                    (click)="loadInvoices(i + 1)"
                    [disabled]="loading()"
                    class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                    {{ i + 1 }}
                  </button>
                }
              }

              <button
                (click)="loadInvoices(response()!.meta.current_page + 1)"
                [disabled]="response()!.meta.current_page >= response()!.meta.last_page || loading()"
                class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <span class="sr-only">{{ t('pagination.next') }}</span>
                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    }
  } @else {
    <div class="mt-8 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h3 class="mt-2 text-sm font-semibold text-gray-900">{{ t('empty_state.title') }}</h3>
      <p class="mt-1 text-sm text-gray-500">{{ t('empty_state.description') }}</p>
    </div>
  }
</div>
