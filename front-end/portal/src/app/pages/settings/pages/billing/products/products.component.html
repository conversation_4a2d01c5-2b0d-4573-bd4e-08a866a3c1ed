<ng-container *transloco="let t; prefix: 'pages.settings.billing.products'">
  <div>
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <!-- Header -->
      <div class="mx-auto max-w-4xl text-center">
        <h2 class="text-base font-semibold leading-7 text-indigo-600">{{ t('page_title') }}</h2>
        <p class="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">{{ t('page_description') }}</p>
      </div>
      <p class="mx-auto mt-6 max-w-2xl text-center text-lg leading-8 text-gray-600">
        {{ t('subtitle') }}
      </p>

      @if (loading()) {
        <div class="flex justify-center items-center py-16">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      } @else if (products() && products()!.length > 0) {
        <!-- Pricing grid -->
        <div class="mx-auto mt-16 max-w-2xl rounded-3xl ring-1 ring-gray-200 sm:mt-20 lg:mx-0 lg:flex lg:max-w-none"
             [class.lg:max-w-4xl]="products()!.length === 2"
             [class.lg:max-w-6xl]="products()!.length === 3"
             [class.lg:max-w-7xl]="products()!.length >= 4">
          @for (product of products(); track product.id; let isFirst = $first; let isLast = $last; let isEven = $even) {
            <div class="p-8 sm:p-10 lg:flex-auto bg-white flex flex-col"
                 [class.lg:rounded-l-3xl]="isFirst"
                 [class.lg:rounded-r-3xl]="isLast"
                 [class.rounded-3xl]="products()!.length === 1"
                 [class.lg:border-r]="!isLast"
                 [class.border-gray-200]="!isLast">

              <div class="flex items-center justify-between gap-x-2">
                <h3 class="text-2xl font-bold tracking-tight text-gray-900">{{ product.name }}</h3>
                <div class="flex items-center gap-2">
                  @if(subscription()?.active_schedule && product.id === subscription()?.active_schedule?.product_id) {
                    <div class="bg-indigo-600/10 text-indigo-600 font-medium py-1 px-2 text-xs rounded-full">
                      <p>{{ t('product_card.upcoming') }}</p>
                    </div>
                  }
                  @if (product.limit !== null) {
                    <p class="rounded-full bg-indigo-600/10 px-2.5 py-1 text-xs font-semibold leading-5 text-indigo-600">
                      {{ product.limit }} {{ t('product_card.limit_label') }}
                    </p>
                  }
                </div>
              </div>

              @if (product.description) {
                <p class="mt-6 text-base leading-7 text-gray-600 w-86 flex-1">{{ product.description }}</p>
              }

              @if (product.monthly_price) {
                <div class="mt-10 flex items-center gap-x-4">
                  <h4 class="flex-none text-sm font-semibold leading-6 text-indigo-600">{{ t('product_card.pricing.monthly') }}</h4>
                  <div class="h-px flex-auto bg-gray-100"></div>
                </div>
              }

              @if (product.monthly_price) {
                <div class="flex items-baseline gap-x-2">
                  <span class="text-5xl font-bold tracking-tight text-gray-900">{{ product.monthly_price.price / 100 | currency: product.monthly_price.currency }}</span>
                </div>
              }

              @if (product.yearly_price) {
                <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">{{ t('product_card.pricing.yearly') }}</span>
                    <span class="text-sm font-semibold text-green-600">{{ t('product_card.save_yearly') }}</span>
                  </div>
                  <div class="mt-2 flex items-baseline gap-x-2">
                    <span class="text-5xl font-bold tracking-tight text-gray-900">{{ product.yearly_price.price / 100 | currency: product.yearly_price.currency }}</span>
                  </div>
                </div>
              }

              @if (product.features && product.features.length > 0) {
                <ul role="list" class="mt-8 space-y-3 text-sm leading-6 text-gray-600">
                  @for (feature of product.features; track $index) {
                    <li class="flex gap-x-3">
                      <svg class="h-6 w-5 flex-none text-indigo-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                      </svg>
                      {{ feature }}
                    </li>
                  }
                </ul>
              }


              <div class="mt-10 flex">
                <button
                  (click)="subscribe(product)"
                  [ngClass]="{
                      '!bg-white !border-indigo-600 !text-indigo-600 !hover:bg-white': product.id === subscription()?.product_id
                    }"
                  class="w-full rounded-md border border-transparent bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  [disabled]="subscribeLoading()"
                >
                  @if (subscribeLoading()) {
                    <span class="flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      {{ t('loading') }}
                      </span>
                  } @else if(product.id === subscription()?.product_id) {
                    {{ t('product_card.selected_plan') }}
                  } @else {
                    {{ t('product_card.select_plan') }}
                  }
                </button>
              </div>
            </div>
          }
        </div>
      } @else {
        <!-- Empty State -->
        <div class="text-center py-24">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">{{ t('no_products.title') }}</h3>
          <p class="mt-1 text-sm text-gray-500">{{ t('no_products.description') }}</p>
        </div>
      }
    </div>
  </div>
</ng-container>
