import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import { InvoiceStatus } from '@api/billing/enums/invoice-status.enum';
import { Invoice } from '@api/billing/models/invoice.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';

import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { DatePipe, DecimalPipe, NgClass } from '@angular/common';
import { InvoiceService } from '@api/billing/services/invoice.service';
import { catchError, filter, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { saveAs } from 'file-saver';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-invoices',
  imports: [TranslocoDirective, DatePipe, DecimalPipe, NgClass],
  templateUrl: './invoices.component.html',
  providers: [DatePipe],
})
export class InvoicesComponent implements OnInit {
  public loading = signal<boolean>(false);
  public response = signal<PaginatedResponse<Invoice> | null>(null);

  public readonly status = InvoiceStatus;

  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private invoiceService = inject(InvoiceService);
  private datePipe = inject(DatePipe);
  private changeDetectorRef = inject(ChangeDetectorRef);

  public ngOnInit(): void {
    this.loadInvoices();
  }

  public loadInvoices(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.invoiceService
      .index({
        page,
      })
      .pipe(
        filter((response): response is PaginatedResponse<Invoice> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public download(invoice: Invoice, state: { loading: boolean }): void {
    if (state.loading) {
      return;
    }

    state.loading = true;

    this.invoiceService
      .download(invoice)
      .pipe(
        tap((response) => {
          saveAs(
            response,
            `linkmyagency_invoice_${invoice.number}_${this.datePipe.transform(invoice.invoiced_date, 'dd-MM-yyyy')}.pdf`,
          );
          state.loading = false;
          this.changeDetectorRef.detectChanges();
        }),
        catchError((err) => {
          this.toastService.error();
          state.loading = false;
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
