import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { loadStripe, Stripe, StripeElements } from '@stripe/stripe-js';
import { catchError, Subject, tap } from 'rxjs';
import { ConfigService } from '@services/config.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TranslocoPipe, TranslocoService } from '@jsverse/transloco';
import { ModalComponent } from '@components/modal/modal.component';

import { BillingService } from '@api/billing/services/billing.service';
import { ToastService } from '@services/toast.service';
@Component({
  selector: 'app-method',
  standalone: true,
  imports: [TranslocoPipe, ModalComponent],
  templateUrl: './method.component.html',
})
export class MethodComponent implements OnInit {
  private form = signal<StripeElements | null>(null);
  public isLoading = signal<boolean>(false);
  public submitting = signal<boolean>(false);
  public close$: Subject<void> = new Subject<void>();

  private stripe: Stripe | null = null;
  private clientSecret: string | undefined;

  private configService = inject(ConfigService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private billingService = inject(BillingService);

  public ngOnInit() {
    this.isLoading.set(true);
    this.initStripe();
  }

  public close(): void {
    this.close$.next();
    this.close$.complete();
  }

  public async savePaymentMethod(): Promise<void> {
    const form = this.form();

    if (!this.stripe || !form) {
      return;
    }

    this.submitting.set(true);

    await this.stripe.confirmSetup({
      elements: form,
      confirmParams: {
        return_url:
          this.configService.config?.stripe.return_url ??
          'https://dashboard.linkhealthmonitor.dev:4200/#/billing/information',
      },
    });

    this.submitting.set(false);

    this.toastService.success();
  }

  private async initStripe(): Promise<void> {
    const stripeKey = this.configService.config?.stripe.key;

    if (!stripeKey) {
      this.close();
      return;
    }

    this.stripe = await loadStripe(stripeKey);

    this.getSetupIntent();
  }

  private getSetupIntent(): void {
    this.billingService
      .setupIntent()
      .pipe(
        tap((res) => {
          this.clientSecret = res.data.client_secret;

          this.initForm();
        }),
        catchError((err) => {
          this.toastService.error();
          this.close();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private initForm(): void {
    if (!this.stripe) {
      this.close();
      return;
    }

    const options = {
      clientSecret: this.clientSecret,
      appearance: {},
    };

    const form = this.stripe.elements(options);

    const paymentElement = form.create('payment');

    this.form.set(form);

    paymentElement.mount('#payment-element');

    paymentElement.on('loaderstart', () => {
      this.isLoading.set(false);
    });
  }
}
