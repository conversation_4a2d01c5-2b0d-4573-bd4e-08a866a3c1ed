import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { Product } from '@api/billing/models/product.interface';
import { Subscription } from '@api/billing/models/subscription.interface';
import { AuthState } from '@states/auth.state';
import { BillingService } from '@api/billing/services/billing.service';

import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { RecurringInterval } from '@api/billing/enums/recurring-interval.enum';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CurrencyPipe, NgClass } from '@angular/common';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-products',
  imports: [TranslocoDirective, CurrencyPipe, NgClass],
  templateUrl: './products.component.html',
})
export class ProductsComponent implements OnInit {
  public products = signal<Product[] | null>(null);
  public subscription = signal<Subscription | null>(null);
  public loading = signal<boolean>(false);
  public subscribeLoading = signal<boolean>(false);

  private authService = inject(AuthState);
  private billingService = inject(BillingService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);

  public ngOnInit(): void {
    this.subscription.set(
      this.authService.user()?.account?.subscription ?? null,
    );
    this.loadProducts();
  }

  public subscribe(product: Product): void {
    if (this.subscribeLoading()) {
      return;
    }

    if (
      this.subscription()?.product_id === product.id ||
      this.subscription()?.active_schedule?.product_id === product.id
    ) {
      return;
    }

    this.subscribeLoading.set(true);

    this.billingService
      .subscribe({
        product_id: product.id,
        recurring_interval: RecurringInterval.MONTH,
      })
      .pipe(
        tap((response) => {
          this.subscribeLoading.set(false);

          response.data.product_id = product.id;

          this.subscription.set(response.data);
          this.toastService.success();
        }),
        catchError((err) => {
          let key = 'toasts.error';

          if (err.status === 404) {
            key = 'toasts.missing_payment_method';
          }

          this.subscribeLoading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadProducts(): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.billingService
      .products()
      .pipe(
        tap((response) => {
          this.products.set(response.data);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.toastService.error();
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
