import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  signal,
  computed,
  Input,
  inject,
  DestroyRef,
  Signal,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { UserRole } from '@api/profile/enums/user-role.enum';
import { UserService } from '@api/profile/services/user.service';

import { User } from '@api/profile/models/user.interface';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UserStoreRequest } from '@api/profile/requests/user-store.request';
import { UserRequest } from '@api/profile/requests/user.request';
import { Router } from '@angular/router';
import { ToastService } from '@services/toast.service';

interface UserForm {
  firstname: FormControl<string | null>;
  lastname: FormControl<string | null>;
  email: FormControl<string | null>;
  role: FormControl<UserRole>;
  image: FormControl<File | null>;
}

@Component({
  selector: 'app-detail',
  imports: [ReactiveFormsModule, TranslocoDirective, TranslocoPipe],
  templateUrl: './detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DetailComponent implements OnInit {
  @Input('id') id!: number;
  public loading = signal<boolean>(false);
  public saving = signal<boolean>(false);
  public form!: FormGroup<UserForm>;
  public imagePreview = signal<string | null>(null);

  public user!: User;

  public readonly userRoles = Object.values(UserRole);

  private userService = inject(UserService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);

  public ngOnInit(): void {
    this.loadUser();
  }

  public onImageChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      this.form.controls.image.setValue(file);
      const reader = new FileReader();
      reader.onload = () => {
        this.imagePreview.set(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      this.form.controls.image.setValue(null);
      this.imagePreview.set(null);
    }
  }

  public submit(): void {
    if (this.saving() || !this.form || this.form.invalid) {
      this.form?.markAllAsTouched();
      return;
    }

    this.saving.set(true);

    let service = this.userService.store(this.form.value as UserStoreRequest);

    if (this.user) {
      service = this.userService.update(
        this.user,
        this.form.value as UserRequest,
      );
    }

    service
      .pipe(
        tap(() => {
          this.router.navigate(['/settings/users']);
          this.saving.set(false);
          this.toastService.success();
        }),
        catchError((err) => {
          this.saving.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private initForm(): void {
    this.form = new FormGroup<UserForm>({
      firstname: new FormControl(this.user?.firstname ?? null, {
        nonNullable: true,
        validators: [Validators.required],
      }),
      lastname: new FormControl(this.user?.lastname ?? null, {
        nonNullable: true,
        validators: [Validators.required],
      }),
      role: new FormControl(this.user?.role ?? UserRole.USER, {
        nonNullable: true,
        validators: [Validators.required],
      }),
      email: new FormControl(
        { value: this.user?.email ?? null, disabled: !!this.user?.email },
        [Validators.required],
      ),
      image: new FormControl(null),
    });
  }

  private loadUser(): void {
    if (!this.id) {
      this.initForm();
      return;
    }

    this.loading.set(true);

    this.userService
      .show(this.id)
      .pipe(
        tap((response) => {
          this.user = response.data;
          this.initForm();
          this.loading.set(false);
        }),
        catchError((err) => {
          this.toastService.error();
          this.initForm();
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
