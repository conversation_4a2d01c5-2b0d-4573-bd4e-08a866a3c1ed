 <div *transloco="let t; read: 'pages.settings.general'" [formGroup]="form">
  <!-- Header Section -->
  <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl">
    <div class="px-6 py-6 sm:px-8">
      <div class="flex justify-between items-start">
        <div>
          <h2 class="text-xl font-semibold leading-7 text-gray-900">{{ t('title') }}</h2>
          <p class="mt-2 text-sm leading-6 text-gray-600">{{ t('description') }}</p>
        </div>
      </div>
    </div>

    <!-- Form Section -->
    <div class="border-t border-gray-200 px-6 py-6 sm:px-8">
      <form class="space-y-6">
        <!-- Email Field -->
        <div class="form-group-new">
          <label for="email">
            {{ t('fields.name') }}
          </label>
          <input
            id="name"
            name="name"
            type="text"
            autocomplete="name"
            [formControl]="form.controls.name"
            [placeholder]="t('placeholders.name')">
        </div>

        <!-- Address Section -->
        <div class="border-t border-gray-200 pt-6 form-group-new">
          <label for="email">
            {{ t('fields.about') }}
          </label>

          @if(editor(); as editor) {
            <div class="NgxEditor__Wrapper mt-2">
              <ngx-editor-menu [editor]="editor"> </ngx-editor-menu>
              <ngx-editor
                [editor]="editor"
                [formControl]="form.controls.about"
                [placeholder]="t('placeholders.about')"
              ></ngx-editor>
            </div>
          }
        </div>
      </form>
    </div>

    <!-- Action Section -->
    <div class="border-t border-gray-200 px-6 py-4 sm:px-8">
      <div class="flex justify-end">
        <button
          (click)="submit()"
          [class.--disabled]="form.invalid"
          [class.--loading]="loading()"
          type="button"
          class="btn --blue">
          <span>{{ 'general.buttons.save' | transloco }}</span>
        </button>
      </div>
    </div>
  </div>
</div>
