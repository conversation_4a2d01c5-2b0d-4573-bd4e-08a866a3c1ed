import {
  Component,
  Destroy<PERSON>ef,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import {
  NgxEditorComponent,
  NgxEditorMenuComponent,
  Editor,
  toHTML,
  toDoc,
} from 'ngx-editor';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { AuthState } from '@states/auth.state';
import { AccountRequest } from '@api/profile/requests/account.request';
import { AccountService } from '@api/profile/services/account.service';
import { ToastService } from '@services/toast.service';
import { catchError, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

interface Form {
  name: FormControl<string | null>;
  about: FormControl<Record<string, unknown> | null>;
}

@Component({
  selector: 'app-general',
  imports: [
    NgxEditorMenuComponent,
    NgxEditorComponent,
    FormsModule,
    TranslocoDirective,
    ReactiveFormsModule,
    TranslocoPipe,
  ],
  templateUrl: './general.component.html',
})
export class GeneralComponent implements OnInit, OnDestroy {
  public editor = signal<Editor | null>(null);
  public loading = signal<boolean>(false);

  public form: FormGroup<Form> = new FormGroup<Form>({
    name: new FormControl(null, [Validators.required]),
    about: new FormControl(null),
  });

  private authState = inject(AuthState);
  private accountService = inject(AccountService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);

  public ngOnInit(): void {
    this.setForm();
    this.initEditor();
  }

  public ngOnDestroy(): void {
    this.editor()?.destroy();
  }

  public submit(): void {
    if (this.loading() || this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const about = this.form.controls.about.value;

    const body: AccountRequest = {
      name: this.form.controls.name.value as string,
      about: about ? toHTML(about) : null,
    };

    this.loading.set(true);

    this.accountService
      .update(body)
      .pipe(
        tap((response) => {
          this.loading.set(false);
          this.toastService.success();

          this.authState.user.update((user) =>
            user
              ? {
                  ...user,
                  account: response.data,
                }
              : null,
          );
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private initEditor(): void {
    this.editor.set(new Editor());
  }

  private setForm(): void {
    const account = this.authState.user()?.account;

    if (!account) {
      return;
    }

    this.form.patchValue({
      name: account.name,
      about: account.about ? toDoc(account.about) : null,
    });
  }
}
