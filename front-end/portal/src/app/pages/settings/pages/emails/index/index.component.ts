import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { PaginationComponent } from '@components/pagination/pagination.component';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { Email } from '@api/email/models/email.interface';
import { Language } from '@interfaces/language.interface';
import { EmailService } from '@api/email/services/email.service';

import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  tap,
} from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LanguageService } from '@services/language.service';
import { DropdownComponent } from '@components/dropdown/dropdown.component';
import { DatePipe } from '@angular/common';
import { AddLanguageService } from '@pages/settings/pages/emails/index/add-language/add-language.service';
import { PreviewService } from '@pages/settings/pages/emails/index/preview/preview.service';
import { DeleteService } from '@pages/settings/pages/emails/index/delete/delete.service';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-index',
  imports: [
    PaginationComponent,
    ReactiveFormsModule,
    RouterLink,
    TranslocoDirective,
    TranslocoPipe,
    DropdownComponent,
    DatePipe,
  ],
  templateUrl: './index.component.html',
})
export class IndexComponent implements OnInit {
  public response = signal<PaginatedResponse<Email> | null>(null);
  public loading = signal<boolean>(false);
  public languages = signal<Language[]>([]);

  public search: FormControl<string | null> = new FormControl(null);
  public language: FormControl<string | null> = new FormControl(null);

  private emailService = inject(EmailService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);
  private activatedRoute = inject(ActivatedRoute);
  private languageService = inject(LanguageService);
  private addLanguageService = inject(AddLanguageService);
  private deleteService = inject(DeleteService);
  private previewService = inject(PreviewService);

  public ngOnInit(): void {
    this.listenToSearch();
    this.listenToLanguage();
    this.loadLanguages();
    this.loadEmails();
  }

  public loadEmails(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.emailService
      .index({
        page,
        search: this.search.value,
        language: this.language.value,
      })
      .pipe(
        filter((response): response is PaginatedResponse<Email> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public navigateToDetail(email: Email): void {
    this.router.navigate([email.id], { relativeTo: this.activatedRoute });
  }

  public async addLanguage(email: Email): Promise<void> {
    const result = await this.addLanguageService.show(email);

    if (!result) {
      return;
    }

    this.navigateToDetail(result);
  }

  public async delete(email: Email): Promise<void> {
    const result = await this.deleteService.show(email);

    if (!result) {
      return;
    }

    this.loadEmails();
  }

  public preview(email: Email): void {
    this.previewService.show(email);
  }

  private listenToSearch(): void {
    this.search.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap(() => {
          this.loadEmails();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private listenToLanguage(): void {
    this.language.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap(() => {
          this.loadEmails();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadLanguages(): void {
    this.languageService
      .load()
      .pipe(
        tap((response) => {
          this.languages.set(response);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
