import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { ModalComponent } from '@components/modal/modal.component';
import { catchError, Subject, tap } from 'rxjs';
import { Email } from '@api/email/models/email.interface';
import { Language } from '@interfaces/language.interface';
import { LanguageService } from '@services/language.service';
import { EmailService } from '@api/email/services/email.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { EmailAddLanguageRequest } from '@api/email/requests/email-add-language.request';

import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { ToastService } from '@services/toast.service';

interface Form {
  language: FormControl<string | null>;
}

@Component({
  selector: 'app-add-language',
  standalone: true,
  imports: [
    ModalComponent,
    TranslocoDirective,
    TranslocoPipe,
    ReactiveFormsModule,
  ],
  templateUrl: './add-language.component.html',
})
export class AddLanguageComponent implements OnInit {
  public email!: Email;
  public close$: Subject<Email | null> = new Subject();
  public languages = signal<Language[]>([]);
  public form = signal<FormGroup<Form> | null>(null);
  public loading = signal<boolean>(false);

  private languageService = inject(LanguageService);
  private emailService = inject(EmailService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);

  public ngOnInit(): void {
    this.loadLanguages();
    this.initForm();
  }

  public close(email: Email | null = null): void {
    this.close$.next(email);
    this.close$.complete();
  }

  public submit(): void {
    const form = this.form();

    if (!form || form.invalid || this.loading() || !this.email) {
      form?.markAllAsTouched();
      return;
    }

    this.loading.set(true);

    const body: EmailAddLanguageRequest = {
      language: form.controls.language.value ?? '',
    };

    this.emailService
      .addLanguage(this.email, body)
      .pipe(
        tap((response) => {
          this.close(response.data);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.toastService.error();
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private loadLanguages(): void {
    this.languageService
      .load()
      .pipe(
        tap((response) => {
          this.languages.set(response);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private initForm(): void {
    const form = new FormGroup<Form>({
      language: new FormControl(null, [Validators.required]),
    });

    this.form.set(form);
  }
}
