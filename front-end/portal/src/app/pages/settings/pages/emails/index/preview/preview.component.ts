import { Component, DestroyRef, inject, signal } from '@angular/core';
import { Email } from '@api/email/models/email.interface';
import { EmailService } from '@api/email/services/email.service';
import { catchError, Subject, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ModalComponent } from '@components/modal/modal.component';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';

import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-preview',
  standalone: true,
  imports: [ModalComponent, TranslocoDirective, TranslocoPipe],
  templateUrl: './preview.component.html',
})
export class PreviewComponent {
  public email!: Email;
  public close$: Subject<void> = new Subject();
  public loading = signal<boolean>(false);

  private emailService = inject(EmailService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);

  public close(): void {
    this.close$.next();
    this.close$.complete();
  }

  public submit(): void {
    if (!this.email || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.emailService
      .preview(this.email)
      .pipe(
        tap(() => {
          this.loading.set(false);
          this.toastService.success();
          this.close();
        }),
        catchError((err) => {
          this.toastService.error();
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
