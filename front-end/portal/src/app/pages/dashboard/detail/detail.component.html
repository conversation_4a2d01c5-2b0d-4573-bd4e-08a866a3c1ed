<!-- Main Card -->
<div *transloco="let t; read: 'pages.dashboards.detail'" class="bg-white shadow sm:rounded-lg">
  @if(loading()) {
    <div class="w-full h-64 flex justify-center items-center">
      <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-3xl"></i>
    </div>
  } @else {
    <div class="px-4 py-5 sm:px-6">
      <div class="flex items-center justify-between">
        @for(step of steps(); track $index; let index = $index; let last = $last) {
          <div class="flex items-center">
            <div [class.!bg-eaglo-blue]="index <= currentIndex()" class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-300 text-white text-center">
              <span>{{ index + 1 }}</span>
            </div>
            <div [class.!text-eaglo-blue]="index <= currentIndex()" class="ml-2 text-sm font-medium text-gray-500">
              <span>{{ t('steps.' + step) }}</span>
            </div>
          </div>
        }
      </div>
    </div>

    <!-- Card Content -->
    <div [formGroup]="form" class="px-4 py-5 sm:p-6">
      @if(currentStep() === step.GENERAL) {
        <dashboard-detail-general [form]="form.controls.general" class="block"></dashboard-detail-general>
      }

      @if(currentStep() === step.SOURCES) {
        <dashboard-detail-sources [form]="form.controls.sources" class="block"></dashboard-detail-sources>
      }

      @if(currentStep() === step.PAGES) {
        <dashboard-detail-pages [form]="form" class="block"></dashboard-detail-pages>
      }

      @if(currentStep() === step.RECIPIENT) {
        <dashboard-detail-recipient [form]="form.controls.recipient" class="block"></dashboard-detail-recipient>
      }

      @if(currentStep() === step.KPIS) {
        <dashboard-detail-kpis [form]="form.controls.kpis"></dashboard-detail-kpis>
      }
    </div>

    <div class="px-4 gap-x-4">
      <div class="border-t border-gray-200 w-full py-4 flex justify-between items-center">

        @if(currentIndex() === 0) {
          <a routerLink="/dashboards" class="btn --outline --small">{{ 'general.buttons.cancel' | transloco }}</a>
        } @else {
          <button (click)="previousStep()" class="btn --outline --small">{{ 'general.buttons.previous' | transloco }}</button>
        }

        @if((currentIndex() + 1) === steps().length) {
          <button (click)="submit()" [class.--disabled]="form.invalid" class="btn --small">{{ 'general.buttons.save' | transloco }}</button>
        } @else {
          <button (click)="nextStep()" [class.--disabled]="!nextStepAllowed()" class="btn --small">{{ 'general.buttons.next' | transloco }}</button>
        }
      </div>
    </div>
  }
</div>
