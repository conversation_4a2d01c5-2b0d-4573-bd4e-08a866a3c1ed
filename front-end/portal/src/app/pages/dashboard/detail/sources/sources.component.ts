import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  Input,
} from '@angular/core';
import {
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
} from '@angular/forms';
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco';
import { SourcesForm } from '../dashboard-form.interface';
import { GoogleAdAccountService } from '@api/google/services/google-ad-account.service';
import { combineLatest, map, Observable } from 'rxjs';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { SelectOption } from '@app/interfaces/select-option.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ConnectedSelectComponent } from '../../../../components/form-inputs/connected-select/connected-select.component';

@Component({
  selector: 'dashboard-detail-sources',
  imports: [TranslocoDirective, ReactiveFormsModule, ConnectedSelectComponent],
  templateUrl: './sources.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SourcesComponent {
  @Input({ required: true }) form!: FormGroup<SourcesForm>;

  private googleAdAccountService = inject(GoogleAdAccountService);
  private destroyRef = inject(DestroyRef);
  private translocoService = inject(TranslocoService);

  public googleAdAccountSourceCallback = (
    page: number,
    filters: { [key: string]: any },
    search: string | null | undefined,
  ): Observable<PaginatedResponse<SelectOption<number>>> => {
    return this.mapToSourceCallback(
      this.googleAdAccountService,
      this.form.controls.google_ad_account_ids,
      page,
      search,
    );
  };

  public addControl(
    controls: FormArray<FormControl<SelectOption<number> | null>>,
  ): void {
    controls.push(new FormControl(null));
  }

  public removeControl(
    controls: FormArray<FormControl<SelectOption<number> | null>>,
    index: number,
  ): void {
    controls.removeAt(index);
  }

  private mapToSourceCallback = (
    service: any,
    form: FormArray<FormControl<SelectOption<number> | null>>,
    page: number,
    search: string | null | undefined,
  ): Observable<PaginatedResponse<SelectOption<number>>> => {
    const mapItemToSelectOption = (item: any): SelectOption<number> => ({
      value: item.id,
      label: item.name ?? this.translocoService.translate('general.unknown'),
      description: item.external_id,
      disabled:
        form.controls.filter((control) => control.value?.value === item.id)
          .length > 0,
    });

    const index = service.index({ page, search }).pipe(
      map(
        (
          response: PaginatedResponse<any>,
        ): PaginatedResponse<SelectOption<number>> => ({
          ...response,
          data: response.data.map((item) => mapItemToSelectOption(item)),
        }),
      ),
      takeUntilDestroyed(this.destroyRef),
    );

    const items = form.controls;

    if (!search && !!items && (items.length ?? 0) > 0) {
      const services: Array<any> | undefined = items
        .filter((control) => !!control.value)
        .map((item) => service.show(item.value?.value));

      return combineLatest([index, ...services]).pipe(
        map(([index, ...show]): PaginatedResponse<SelectOption<number>> => {
          show.forEach((item) => {
            const itemAlreadyInIndex =
              index.data
                .map((entry: SelectOption<number>) => entry.value)
                // @ts-ignore
                .indexOf(item.data.id) !== -1;

            if (!itemAlreadyInIndex) {
              // @ts-ignore
              index.data.push(mapItemToSelectOption(item.data));
            }
          });
          return index;
        }),
      );
    }

    return index;
  };
}
