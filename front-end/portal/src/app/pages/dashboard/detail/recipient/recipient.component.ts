import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ConnectedSelectComponent } from '../../../../components/form-inputs/connected-select/connected-select.component';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TranslocoDirective } from '@jsverse/transloco';
import { RecipientForm } from '../dashboard-form.interface';

@Component({
  selector: 'dashboard-detail-recipient',
  imports: [ReactiveFormsModule, TranslocoDirective],
  templateUrl: './recipient.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RecipientComponent {
  @Input({ required: true }) form!: FormGroup<RecipientForm>;
}
