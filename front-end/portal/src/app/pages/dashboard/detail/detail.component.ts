import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  Input,
  OnInit,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { PageType } from '@api/dashboard/enums/page-type.enum';
import { SectionType } from '@api/dashboard/enums/section-type.enum';
import { DashboardService } from '@api/dashboard/services/dashboard.service';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';

import {
  DashboardForm,
  GeneralForm,
  KpiEntryForm,
  KpiForm,
  PageForm,
  RecipientForm,
  SectionForm,
  SourcesForm,
} from './dashboard-form.interface';
import { PagesComponent } from './pages/pages.component';
import { catchError, debounceTime, distinctUntilChanged, tap } from 'rxjs';
import { GeneralComponent } from './general/general.component';
import { Router, RouterLink } from '@angular/router';
import {
  DashboardKpiRequest,
  DashboardPageRequest,
  DashboardRequest,
  DashboardSectionRequest,
} from '@api/dashboard/requests/dashboard.request';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { SourcesComponent } from './sources/sources.component';
import { DashboardSourceType } from '@api/dashboard/enums/dashboard-source-type.enum';
import { SelectOption } from '@app/interfaces/select-option.interface';
import { RecipientComponent } from './recipient/recipient.component';
import { WidgetType } from '@api/dashboard/enums/widget-type.enum';
import { KpisComponent } from './kpis/kpis.component';
import { getYear } from 'date-fns';
import { uniqueYearValidator } from '@app/validators/unique-year-validator';
import { DashboardBusinessType } from '@api/dashboard/enums/dashboard-business-type.enum';
import { ToastService } from '@services/toast.service';
import { DashboardConfigurationResponse } from '@api/dashboard/responses/dashboard-configuration.response';

enum Step {
  GENERAL = 'GENERAL',
  RECIPIENT = 'RECIPIENT',
  SOURCES = 'SOURCES',
  PAGES = 'PAGES',
  KPIS = 'KPIS',
}

@Component({
  selector: 'app-detail',
  imports: [
    TranslocoDirective,
    ReactiveFormsModule,
    TranslocoPipe,
    PagesComponent,
    GeneralComponent,
    RouterLink,
    SourcesComponent,
    RecipientComponent,
    KpisComponent,
  ],
  templateUrl: './detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DetailComponent implements OnInit {
  @Input('id') dashboardId!: number;
  public loading = signal<boolean>(false);
  public saving = signal<boolean>(false);
  public nextStepAllowed = signal<boolean>(false);
  public currentStep = signal<Step>(Step.PAGES);
  public currentIndex = signal<number>(0);
  public dashboard = signal<Dashboard | null>(null);
  private configuration = signal<DashboardConfigurationResponse>({});

  public form: FormGroup<DashboardForm> = new FormGroup<DashboardForm>({
    general: new FormGroup<GeneralForm>({
      name: new FormControl(null, [Validators.required]),
      user_id: new FormControl(null, [Validators.required]),
      business_type: new FormControl(DashboardBusinessType.E_COMMERCE, [
        Validators.required,
      ]),
    }),
    pages: new FormArray<FormGroup<PageForm>>([]),
    sources: new FormGroup<SourcesForm>({
      google_ad_account_ids: new FormArray<
        FormControl<SelectOption<number> | null>
      >([]),
    }),
    recipient: new FormGroup<RecipientForm>({
      company: new FormControl(null, [Validators.required]),
    }),
    kpis: new FormArray<FormGroup<KpiForm>>([]),
  });

  public readonly step = Step;
  public steps = signal<Step[]>([
    Step.GENERAL,
    Step.RECIPIENT,
    Step.SOURCES,
    Step.PAGES,
  ]);

  private dashboardService = inject(DashboardService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);

  public ngOnInit(): void {
    this.initForm();
    this.loadDashboard();
  }

  public nextStep(): void {
    if (
      this.currentIndex() + 1 === this.steps.length ||
      !this.nextStepAllowed()
    ) {
      return;
    }

    this.currentIndex.update((index) => index + 1);

    const step = this.steps()[this.currentIndex()];
    this.currentStep.set(step);
  }

  public previousStep(): void {
    if (this.currentIndex() === 0) {
      return;
    }

    this.currentIndex.update((index) => index - 1);
    const step = this.steps()[this.currentIndex()];
    this.currentStep.set(step);
  }

  public submit(): void {
    if (this.saving() || !this.form || this.form.invalid) {
      this.form?.markAllAsTouched();
      return;
    }

    this.saving.set(true);

    const pages = this.form.controls.pages.controls
      .filter((page) => page.controls.enabled.value)
      .map((page) => {
        const sections = page.controls.sections.controls
          .filter((section) => section.controls.enabled.value)
          .map(
            (section) =>
              ({
                type: section.controls.type.value,
              }) as DashboardSectionRequest,
          );

        return {
          type: page.controls.type.value,
          sections,
        } as DashboardPageRequest;
      });

    const body: DashboardRequest = {
      general: {
        name: this.form.controls.general.controls.name.value as string,
        user_id: this.form.controls.general.controls.user_id.value
          ?.value as number,
        business_type: this.form.controls.general.controls.business_type
          .value as DashboardBusinessType,
      },
      pages,
      sources: {
        google_ad_account_ids:
          this.form.controls.sources.controls.google_ad_account_ids.controls
            .filter((control) => !!control.value?.value)
            .map((control) => control.value?.value) as number[],
      },
      recipient: {
        company: this.form.controls.recipient.controls.company.value as string,
      },
      kpis: this.form.controls.kpis.controls.map((kpi) => ({
        type: kpi.controls.type.value as WidgetType,
        entries: kpi.controls.entries.controls.map((entry) => ({
          target: entry.controls.target.value as number,
          year: getYear(entry.controls.year.value as Date),
          unit: entry.controls.unit.value,
        })),
      })) as DashboardKpiRequest[],
    };

    let service = this.dashboardService.store(body);

    const dashboard = this.dashboard();

    if (dashboard) {
      service = this.dashboardService.update(dashboard, body);
    }

    service
      .pipe(
        tap(() => {
          this.saving.set(false);
          this.toastService.success();
          this.router.navigate(['dashboards']);
        }),
        catchError((err) => {
          this.saving.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private async initForm(): Promise<void> {
    const configuration = await this.dashboardService
      .configuration()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .toPromise();

    if (!configuration) {
      this.toastService.error();
      return;
    }

    this.configuration.set(configuration.data);

    Object.keys(this.configuration()).forEach((page) => {
      const pageForm = new FormGroup<PageForm>({
        enabled: new FormControl(true, {
          nonNullable: true,
        }),
        type: new FormControl(page as PageType, { nonNullable: true }),
        sections: new FormArray<FormGroup<SectionForm>>([]),
      });

      Object.keys(this.configuration()[page]).forEach((section) => {
        const sectionForm = new FormGroup<SectionForm>({
          enabled: new FormControl(true, {
            nonNullable: true,
          }),
          type: new FormControl(section as SectionType, { nonNullable: true }),
          placement: new FormControl(null, {
            nonNullable: true,
          }),
        });

        sectionForm.controls.enabled.valueChanges
          .pipe(
            tap((value) => {
              this.toggleWidgetKpis();

              if (!value) {
                return;
              }

              pageForm.controls.enabled.setValue(true, {
                onlySelf: true,
                emitEvent: false,
              });
            }),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe();

        pageForm.controls.sections.push(sectionForm);
      });

      pageForm.controls.enabled.valueChanges
        .pipe(
          tap((value) => {
            pageForm.controls.sections.controls.forEach((section) =>
              section.controls.enabled.setValue(value),
            );
          }),
          takeUntilDestroyed(this.destroyRef),
        )
        .subscribe();

      this.form.controls.pages.push(pageForm);
    });

    this.toggleWidgetKpis();

    this.form.controls.general.controls.business_type.valueChanges
      .pipe(
        distinctUntilChanged(),
        tap(() => {
          this.toggleWidgetKpis();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.form.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap((value) => {
          this.nextStepAllowed.set(this.isNextStepAllowed());
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.nextStepAllowed.set(this.isNextStepAllowed());
  }

  private isNextStepAllowed(): boolean {
    if (this.currentStep() === Step.GENERAL) {
      return this.form.controls.general.valid;
    }

    if (this.currentStep() === Step.KPIS) {
      return this.form.controls.kpis.valid;
    }

    return true;
  }

  private loadDashboard(): void {
    if (!this.dashboardId) {
      return;
    }

    this.loading.set(true);

    this.dashboardService
      .show(this.dashboardId)
      .pipe(
        tap((response) => {
          this.dashboard.set(response.data);
          this.setForm();
          this.loading.set(false);
        }),
        catchError((err) => {
          this.toastService.error();
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private getMappedFormSources(
    type: DashboardSourceType,
  ): SelectOption<number>[] | null {
    return (
      this.dashboard()
        ?.sources?.filter((source) => source.source_type === type)
        .map(
          (source) =>
            ({ value: source.source_id, label: '' }) as SelectOption<number>,
        ) ?? null
    );
  }

  private setKpis(
    enabled: boolean,
    pageType: PageType,
    sectionType: SectionType,
    type: WidgetType,
  ): void {
    if (!enabled) {
      const filtered = this.form.controls.kpis.controls.filter(
        (group) => group.controls.type.value !== type,
      );

      this.form.controls.kpis.clear();

      filtered.forEach((entry) => this.form.controls.kpis.push(entry));

      if (filtered.length === 0 && this.steps().includes(Step.KPIS)) {
        this.steps().pop();
      }
      return;
    }

    let group = this.form.controls.kpis.controls
      .filter((group) => group.controls.type.value === type)
      .at(0);

    if (!group) {
      group = new FormGroup<KpiForm>({
        type: new FormControl(type, { nonNullable: true }),
        entries: new FormArray<FormGroup<KpiEntryForm>>(
          [],
          [uniqueYearValidator()],
        ),
      });

      this.form.controls.kpis.push(group);
    }

    group.controls.entries.clear();

    const kpis = this.dashboard()
      ?.pages?.filter((page) => page.type === pageType)
      ?.at(0)
      ?.sections?.filter((section) => section.type === sectionType)
      ?.at(0)
      ?.widgets?.filter((widget) => widget.type === type)
      ?.at(0)?.kpis;

    if (kpis) {
      kpis.forEach((kpi) => {
        const entry = new FormGroup<KpiEntryForm>({
          target: new FormControl(kpi.target, [Validators.required]),
          year: new FormControl(new Date(kpi.year, 0, 1), [
            Validators.required,
          ]),
          unit: new FormControl(kpi.unit, [Validators.required]),
        });

        group.controls.entries.push(entry);
      });
    } else {
      const entry = new FormGroup<KpiEntryForm>({
        target: new FormControl(null, [Validators.required]),
        year: new FormControl(new Date(), [Validators.required]),
        unit: new FormControl(null, [Validators.required]),
      });

      group.controls.entries.push(entry);
    }

    if (!this.steps().includes(Step.KPIS)) {
      this.steps().push(Step.KPIS);
    }
  }

  private toggleWidgetKpis() {
    this.form.controls.pages.controls.forEach((page) => {
      page.controls.sections.controls.forEach((section) => {
        Object.keys(
          this.configuration()[page.controls.type.value][
            section.controls.type.value
          ],
        )
          .filter(
            (widget) =>
              this.configuration()[page.controls.type.value][
                section.controls.type.value
              ][widget as WidgetType].has_kpis,
          )
          .forEach((widget) => {
            const enabled =
              (!this.configuration()[page.controls.type.value][
                section.controls.type.value
              ][widget as WidgetType].business_types ||
                (this.form.controls.general.controls.business_type.value &&
                  this.configuration()[page.controls.type.value][
                    section.controls.type.value
                  ][widget as WidgetType].business_types?.includes(
                    this.form.controls.general.controls.business_type.value,
                  ))) ??
              false;

            this.setKpis(
              section.controls.enabled.value && enabled,
              page.controls.type.value,
              section.controls.type.value,
              widget as WidgetType,
            );
          });
      });
    });
  }

  private setForm(): void {
    const dashboard = this.dashboard();

    if (!dashboard) {
      return;
    }

    this.form.patchValue({
      general: {
        name: dashboard.name,
        user_id: !!dashboard?.user
          ? { value: dashboard.user.id, label: '' }
          : null,
        business_type: dashboard?.business_type ?? null,
      },
      recipient: {
        company: dashboard.recipient?.company,
      },
    });

    this.getMappedFormSources(DashboardSourceType.GOOGLE_AD_ACCOUNT)?.forEach(
      (source) => {
        this.form.controls.sources.controls.google_ad_account_ids.push(
          new FormControl<SelectOption<number>>(source),
        );
      },
    );

    this.form.controls.pages.controls.forEach((pageGroup) => {
      const page = dashboard.pages
        ?.filter((page) => page.type === pageGroup.controls.type.value)
        .at(0);

      pageGroup.controls.enabled.setValue(!!page);

      if (!page) {
        return;
      }

      pageGroup.controls.sections.controls.forEach((sectionGroup) => {
        const section = page.sections
          ?.filter(
            (section) => section.type === sectionGroup.controls.type.value,
          )
          .at(0);

        sectionGroup.controls.enabled.setValue(!!section);
      });
    });
  }
}
