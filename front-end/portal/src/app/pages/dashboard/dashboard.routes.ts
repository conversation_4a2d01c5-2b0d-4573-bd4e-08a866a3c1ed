import { Routes } from '@angular/router';
import { subscriptionGuard } from '@guards/subscription.guard';

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    loadComponent: () =>
      import('./index/index.component').then((c) => c.IndexComponent),
  },
  {
    path: 'detail',
    canActivate: [subscriptionGuard],
    children: [
      {
        path: '',
        pathMatch: 'full',
        loadComponent: () =>
          import('./detail/detail.component').then((c) => c.DetailComponent),
      },
      {
        path: ':id',
        loadComponent: () =>
          import('./detail/detail.component').then((c) => c.DetailComponent),
      },
    ],
  },
];
