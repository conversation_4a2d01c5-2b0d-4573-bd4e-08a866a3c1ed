import { DatePipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { DashboardService } from '@api/dashboard/services/dashboard.service';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';

import { catchError, filter, tap } from 'rxjs';
import { DropdownComponent } from '../../../components/dropdown/dropdown.component';
import { DeleteService } from './delete/delete.service';
import { ShareService } from './share/share.service';
import { AuthState } from '@states/auth.state';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-index',
  imports: [
    TranslocoDirective,
    DatePipe,
    TranslocoPipe,
    ReactiveFormsModule,
    RouterLink,
    DropdownComponent,
  ],
  templateUrl: './index.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IndexComponent implements OnInit {
  public response = signal<PaginatedResponse<Dashboard> | null>(null);
  public loading = signal<boolean>(false);
  public hasSubscription = signal<boolean>(false);

  public search: FormControl<string | null> = new FormControl(null);

  private dashboardService = inject(DashboardService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);
  private deleteService = inject(DeleteService);
  private shareService = inject(ShareService);
  private authService = inject(AuthState);

  public ngOnInit(): void {
    this.hasSubscription.set(!!this.authService.user()?.account?.subscription);
    this.loadDashboards();
  }

  public loadDashboards(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.dashboardService
      .index({
        page,
        search: this.search.value,
      })
      .pipe(
        filter((response): response is PaginatedResponse<Dashboard> => true),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public navigateToDetail(dashboard: Dashboard): void {
    if (!this.hasSubscription()) {
      return;
    }

    this.router.navigate(['dashboards', 'detail', dashboard.id]);
  }

  public async delete(dashboard: Dashboard): Promise<void> {
    if (!this.hasSubscription()) {
      return;
    }

    const result = await this.deleteService.show(dashboard);

    if (!result) {
      return;
    }

    this.loadDashboards(this.response()?.meta.current_page ?? 1);
  }

  public share(dashboard: Dashboard): void {
    if (!this.hasSubscription()) {
      return;
    }

    this.shareService.show(dashboard);
  }

  public open(dashboard: Dashboard): void {
    if (!this.hasSubscription()) {
      return;
    }

    window.open(dashboard.url, '_blank');
  }
}
