<div *transloco="let t; read: 'pages.dashboards.index'" class="space-y-4">
  <div class="flex justify-between items-center">
    <div class="form-group !w-64">
      <input
        [formControl]="search"
        [placeholder]="'general.search' | transloco"
      />
    </div>
    <a routerLink="detail" [class.--disabled]="!hasSubscription()" class="btn">
      <i class="fa-regular fa-plus"></i>
      <span>{{ t("add") }}</span>
    </a>
  </div>

  <table class="min-w-full divide-y divide-gray-200 rounded overflow-hidden">
    <thead class="bg-gray-50">
      <tr>
        <th
          scope="col"
          class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider"
        >
          {{ t("table.name") }}
        </th>
        <th
          scope="col"
          class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider"
        >
          {{ t("table.created_at") }}
        </th>
        <th
          scope="col"
          class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider"
        >
          {{ t("table.last_change_at") }}
        </th>
        <th
          scope="col"
          class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider"
        ></th>
      </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
      <!-- Example row, replace with @for when adding logic -->
      @if (!loading() && !!response() && (response()?.data?.length ?? 0) > 0) {
        @for (dashboard of response()?.data; track $index) {
          <tr (click)="navigateToDetail(dashboard)" class="cursor-pointer">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ dashboard.name }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ dashboard.created_at | date }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ dashboard.updated_at | date }}
            </td>
            <td>
              <components-dropdown>
                <button (click)="open(dashboard)" [class.--disabled]="!hasSubscription()" class="btn-dropdown">
                  <i class="fa-regular fa-arrow-up-right-from-square"></i>
                  <span>{{ 'general.buttons.open' | transloco }}</span>
                </button>
                <button (click)="share(dashboard)" [class.--disabled]="!hasSubscription()" class="btn-dropdown">
                  <i class="fa-regular fa-share"></i>
                  <span>{{ 'general.buttons.share' | transloco }}</span>
                </button>
                <button (click)="navigateToDetail(dashboard)" [class.--disabled]="!hasSubscription()" class="btn-dropdown">
                  <i class="fa-regular fa-pen"></i>
                  <span>{{  'general.buttons.edit' | transloco }}</span>
                </button>
                <button (click)="delete(dashboard)" [class.--disabled]="!hasSubscription()" class="btn-dropdown">
                  <i class="fa-regular fa-trash-can"></i>
                  <span>{{ 'general.buttons.delete' | transloco }}</span>
                </button>
              </components-dropdown>
            </td>
          </tr>
        }
      } @else if (loading()) {
        <tr>
          <td colspan="4">
            <div class="flex items-center justify-center h-20">
              <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-3xl"></i>
            </div>
          </td>
        </tr>
      } @else if(!loading() && !!response() && response()?.data?.length === 0) {
        <tr>
          <td colspan="4" class="text-center h-20">{{ 'general.empty_placeholder' | transloco }}</td>
        </tr>
      }
    </tbody>
  </table>
</div>
