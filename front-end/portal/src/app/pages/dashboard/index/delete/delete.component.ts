import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { DashboardService } from '@api/dashboard/services/dashboard.service';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';

import { catchError, Subject, tap } from 'rxjs';
import { ModalComponent } from '../../../../components/modal/modal.component';
import { ToastService } from '@services/toast.service';

@Component({
  selector: 'app-delete',
  imports: [TranslocoDirective, TranslocoPipe, ModalComponent],
  templateUrl: './delete.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeleteComponent {
  public dashboard!: Dashboard;
  public loading = signal<boolean>(false);

  public close$: Subject<boolean> = new Subject();

  private dashboardService = inject(DashboardService);
  private toastService = inject(ToastService);
  private destroyRef = inject(DestroyRef);

  public close(value: boolean = false): void {
    this.close$.next(value);
    this.close$.complete();
  }

  public submit(): void {
    if (!this.dashboard || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.dashboardService
      .delete(this.dashboard)
      .pipe(
        tap(() => {
          this.loading.set(false);
          this.close(true);
          this.toastService.success();
        }),
        catchError((err) => {
          this.loading.set(false);
          this.toastService.error();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
