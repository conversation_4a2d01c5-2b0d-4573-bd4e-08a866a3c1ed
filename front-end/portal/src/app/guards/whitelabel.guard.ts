import { CanActivateFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { AuthState } from '@states/auth.state';

export const whitelabelGuard: CanActivateFn = async () => {
  const authService = inject(AuthState);
  const router = inject(Router);

  if (
    authService.isLoggedIn() &&
    authService.user()?.account?.subscription?.product?.whitelabel
  ) {
    return true;
  }

  router.navigateByUrl('/dashboard');

  return false;
};
