import { CanActivateFn } from '@angular/router';
import { inject } from '@angular/core';
import { AuthState } from '@states/auth.state';
import { ConfigService } from '@services/config.service';

export const subscriptionGuard: CanActivateFn = async () => {
  const configService = inject(ConfigService);
  const authService = inject(AuthState);

  if (configService.config?.local) {
    return true;
  }

  return !!authService.user()?.account?.subscription;
};
