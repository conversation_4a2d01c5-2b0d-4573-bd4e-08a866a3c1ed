import { Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, Subject, tap } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { AuthenticationService } from '@api/auth/services/authentication.service';
import { Me } from '@api/auth/models/me.interface';

export const LOGGED_IN_LOCAL_STORAGE_KEY = 'logged_in';

@Injectable({
  providedIn: 'root',
})
export class AuthState {
  public user = signal<Me | null>(null);

  private impersonating = signal<boolean>(false);

  constructor(
    private router: Router,
    private authenticationService: AuthenticationService,
  ) {}

  public isLoggedIn(): boolean {
    return !!localStorage.getItem(LOGGED_IN_LOCAL_STORAGE_KEY);
  }

  public logout(): void {
    this.user.set(null);

    this.authenticationService.logout().subscribe();
    localStorage.removeItem(LOGGED_IN_LOCAL_STORAGE_KEY);

    this.router.navigate(['/auth']);
  }

  public loadUser(): Observable<DataResponse<Me>> {
    return this.authenticationService.me().pipe(
      tap((response) => {
        this.user.set(response.data);
      }),
    );
  }

  public isImpersonating(): boolean {
    return this.impersonating();
  }

  public setImpersonating(value: boolean): void {
    this.impersonating.set(value);
  }
}
