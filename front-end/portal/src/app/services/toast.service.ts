import { inject, Injectable } from '@angular/core';
import { HotToastService } from '@ngxpert/hot-toast';
import { TranslocoService } from '@jsverse/transloco';

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  private toastService = inject(HotToastService);
  private translocoService = inject(TranslocoService);

  public error(
    title: string | null = null,
    description: string | null = null,
  ): void {
    this.toastService.error(
      '<i class="fa-regular fa-circle-xmark text-red-500 text-2xl"></i>' +
        '<div class="content-container">' +
        '<p class="font-medium">' +
        this.translocoService.translate(title ?? 'general.toasts.error.title') +
        '</p>' +
        '<p>' +
        this.translocoService.translate(
          description ?? 'general.toasts.error.description',
        ) +
        '</p>' +
        '</div>' +
        '<div class="absolute top-2 right-4"><i class="fa-regular fa-xmark text-xl"></i></div>',
      {
        icon: '',
      },
    );
  }

  public success(
    title: string | null = null,
    description: string | null = null,
  ): void {
    this.toastService.success(
      '<i class="fa-regular fa-circle-check text-green-500 text-2xl"></i>' +
        '<div class="content-container">' +
        '<p class="font-medium">' +
        this.translocoService.translate(
          title ?? 'general.toasts.success.title',
        ) +
        '</p>' +
        '<p>' +
        this.translocoService.translate(
          description ?? 'general.toasts.success.description',
        ) +
        '</p>' +
        '</div>' +
        '<div class="absolute top-2 right-4"><i class="fa-regular fa-xmark text-xl"></i></div>',
      {
        icon: '',
      },
    );
  }
}
