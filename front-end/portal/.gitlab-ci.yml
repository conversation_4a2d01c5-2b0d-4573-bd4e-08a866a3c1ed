.image: &image registry.gitlab.com/localium/tooling/docker-containers/php8.3:v0.0.7

#cache:
#  key: front-end_portal_npm
#  policy: pull-push
#  paths:
#    - .npm/

front_end:portal:lint:
  stage: lint
  image: *image
  extends:
    - .front-end:portal:working-directory
  script:
    - npm cache clean --force
    - npm ci
    - npm run lint
  artifacts:
    expire_in: 1 days
    paths:
      - ./front-end/portal/node_modules/

front_end:portal:build:
  stage: build
  image: *image
  extends:
    - .front-end:portal:working-directory
  needs:
    - front_end:portal:lint
  script:
    - npm run prebuild
    - npm run build:prod
  artifacts:
    expire_in: 1 days
    paths:
      - ./front-end/portal/dist/portal/browser

front_end:portal:acc:release:
  stage: staging
  when: manual
  extends: .front-end:portal:deploy
  needs:
    - front_end:portal:build
#  only:
#    - /^release\//
  variables: {
    ONEPASSWORD: "CI Secrets Eaglo/Portal - Acceptance"
  }
  environment:
    name: 'FRONT_END/PORTAL/ACC'
    url: https://dashboard.acc.eaglo.io
    deployment_tier: staging

#front_end:portal:prod:release:
#  stage: production
#  when: manual
#  extends: .front-end:portal:deploy:with_sentry
#  needs:
#    - front_end:portal:build
#  #  only:
#  #    - tags
#  variables: {
#    ONEPASSWORD: "CI Secrets LinkHealthMonitor/Portal - Production"
#  }
#  environment:
#    name: 'FRONT_END/PORTAL/PROD'
#    url: https://dashboard.linkhealthmonitor.com
#    deployment_tier: production


.front-end:portal:deploy:
  image: *image
  before_script:
    # install 1password
    - curl -sSfLo op.zip https://cache.agilebits.com/dist/1P/op2/pkg/v2.19.0/op_linux_amd64_v2.19.0.zip
    - unzip -o op.zip
    - rm op.zip
    - export OP_SERVICE_ACCOUNT_TOKEN=$OP_SERVICE_ACCOUNT
    # set 1password values
    - export USER=$(./op read "op://$ONEPASSWORD/SSH/ssh_user")
    - export PORT=$(./op read "op://$ONEPASSWORD/SSH/ssh_port")
    - export HOST=$(./op read "op://$ONEPASSWORD/Server/server_host")
    - export BASE_PATH=$(./op read "op://$ONEPASSWORD/Server/server_path")
    - export FRONTEND_CONFIG=$(./op read "op://$ONEPASSWORD/config")
    - export SENTRY_TOKEN=$(./op read "op://$ONEPASSWORD/Sentry/token")
    - export SSH="ssh -tt ${USER}@${HOST} -p ${PORT}"
    - export CURRENT_PATH="${BASE_PATH}/current"
    - export RELEASES_PATH="${BASE_PATH}/releases"
    - export RELEASE_PATH="${BASE_PATH}/releases/$CI_JOB_ID"
    # Set front-end config
    - echo "${FRONTEND_CONFIG}" > ./front-end/portal/dist/portal/browser/config.json
    # Replace front-end dynamic values
    - sed -i".bak" "s/{CI_COMMIT_TAG}/$CI_COMMIT_TAG/g" front-end/portal/dist/portal/browser/config.json
    - sed -i".bak" "s/{COMMIT_SHORT_SHA}/$CI_COMMIT_SHORT_SHA/g" front-end/portal/dist/portal/browser/config.json
    - sed -i".bak" "s/{COMMIT_TIMESTAMP}/$CI_COMMIT_TIMESTAMP/g" front-end/portal/dist/portal/browser/config.json
    # SSH
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    # Copy the SSH key
    - echo "$SSH_KEY" > ~/.ssh/id_rsa
    # Set the correct permissions for the SSH folder and key
    - chmod 700 ~/.ssh
    - chmod 600 ~/.ssh/id_rsa
    # Add SSH key
    - ssh-add ~/.ssh/id_rsa
    # Disable strict host key checking
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    # Create release folder
    - $SSH "mkdir -p -m 775 ${RELEASE_PATH}"
    # Compress files
    - tar --exclude="*.*.map" -czf source.tar.gz -C ./front-end/portal/dist/portal/browser .
    # Upload files
    - scp -P${PORT} -r source.tar.gz ${USER}@${HOST}:${RELEASE_PATH}
    # Uncompress files
    - $SSH "tar -xzf ${RELEASE_PATH}/source.tar.gz -C ${RELEASE_PATH}/"
    # Create environment
    - $SSH "echo '$ENV' > ${RELEASE_PATH}/.env"
  script:
    # Active new release
    - $SSH "ln -nfs ${RELEASE_PATH} ${CURRENT_PATH}";
  after_script:
    - curl -sSfLo op.zip https://cache.agilebits.com/dist/1P/op2/pkg/v2.19.0/op_linux_amd64_v2.19.0.zip
    - unzip -o op.zip
    - rm op.zip
    - export OP_SERVICE_ACCOUNT_TOKEN=$OP_SERVICE_ACCOUNT
    # set 1password values
    - export USER=$(./op read "op://$ONEPASSWORD/SSH/ssh_user")
    - export PORT=$(./op read "op://$ONEPASSWORD/SSH/ssh_port")
    - export HOST=$(./op read "op://$ONEPASSWORD/Server/server_host")
    - export BASE_PATH=$(./op read "op://$ONEPASSWORD/Server/server_path")
    - export SSH="ssh -tt ${USER}@${HOST} -p ${PORT}"
    - export RELEASES_PATH="${BASE_PATH}/releases"
    # Clean up old releases
    - $SSH "ls -tr ${RELEASES_PATH} | head -n -5 | xargs --no-run-if-empty -I {} rm -rf ${RELEASES_PATH}/{}"

.front-end:portal:working-directory:
  before_script:
    - cd ./front-end/portal


.front-end:portal:deploy:with_sentry:
  extends: .front-end:portal:deploy
  needs:
    - front_end:portal:build
  before_script:
    - curl -sL https://sentry.io/get-cli/ | SENTRY_CLI_VERSION="2.2.0" bash
    - !reference [.front-end:portal:deploy, before_script]
  after_script:
    - SENTRY_AUTH_TOKEN=$SENTRY_TOKEN
    - SENTRY_ORG=localium
    - SENTRY_PROJECT=portal-7z
    - VERSION=$CI_COMMIT_REF_NAME
    - sentry-cli releases new "$VERSION" -o "$SENTRY_ORG" -p "$SENTRY_PROJECT" --auth-token "$SENTRY_AUTH_TOKEN"
    - sentry-cli releases set-commits "$VERSION" --local -o "$SENTRY_ORG" -p "$SENTRY_PROJECT" --auth-token "$SENTRY_AUTH_TOKEN"
    - sentry-cli releases finalize "$VERSION" -o "$SENTRY_ORG" -p "$SENTRY_PROJECT" --auth-token "$SENTRY_AUTH_TOKEN"
    - sentry-cli sourcemaps upload ./front-end/dist/portal/ --auth-token "$SENTRY_AUTH_TOKEN" --release "$VERSION" -o "$SENTRY_ORG" -p "$SENTRY_PROJECT"
    - !reference [.front-end:portal:deploy, after_script]
